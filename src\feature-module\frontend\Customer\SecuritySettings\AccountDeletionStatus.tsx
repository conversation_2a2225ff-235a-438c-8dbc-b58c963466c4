import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaInfoCircle, FaUndo, FaCalendarAlt } from 'react-icons/fa';
import { <PERSON><PERSON>, Modal, ModalContent, ModalHeader, <PERSON>dal<PERSON>ody, ModalFooter, useDisclosure } from '@heroui/react';
import CustomButton from '../../../components/CustomButton';
import { 
  getLocalDeletionInfo, 
  cancelAccountDeletion, 
  clearLocalDeletionInfo,
  getDaysUntilDeletion,
  AccountDeletionRequest 
} from '../../../../service/accountDeletionService';
import { useAuth } from 'react-oidc-context';

interface AccountDeletionStatusProps {
  uid: string;
  email: string;
}

const AccountDeletionStatus: React.FC<AccountDeletionStatusProps> = ({ uid, email }) => {
  const auth = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [deletionInfo, setDeletionInfo] = useState<AccountDeletionRequest | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error'>('success');
  const [showAlert, setShowAlert] = useState(false);

  useEffect(() => {
    // Check for local deletion info
    const localInfo = getLocalDeletionInfo();
    if (localInfo) {
      setDeletionInfo(localInfo);
    }
  }, []);

  const handleCancelDeletion = async () => {
    if (!deletionInfo) return;

    setIsCancelling(true);
    setShowAlert(false);

    try {
      const result = await cancelAccountDeletion(email, uid);

      if (result.success) {
        setAlertMessage(result.message);
        setAlertType('success');
        setShowAlert(true);

        // Clear local storage and state
        clearLocalDeletionInfo();
        setDeletionInfo(null);

        setTimeout(() => {
          onClose();
          setShowAlert(false);
          // Refresh the page to update the UI
          window.location.reload();
        }, 2000);
      } else {
        throw new Error(result.message || 'Failed to cancel account deletion');
      }
    } catch (error) {
      console.error('Error cancelling account deletion:', error);
      setAlertMessage(error instanceof Error ? error.message : 'Failed to cancel account deletion');
      setAlertType('error');
      setShowAlert(true);
    } finally {
      setIsCancelling(false);
    }
  };

  if (!deletionInfo) {
    return null;
  }

  const daysRemaining = getDaysUntilDeletion(deletionInfo.scheduledDeletionDate);
  const deletionDate = new Date(deletionInfo.scheduledDeletionDate);
  const formattedDate = deletionDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const isExpired = daysRemaining <= 0;

  return (
    <>
      {/* Account Deletion Warning Banner */}
      <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-start gap-3">
          <FaExclamationTriangle className="text-red-600 mt-1" size={20} />
          <div className="flex-1">
            <h3 className="font-semibold text-red-800 mb-2">
              {isExpired ? 'Account Deletion Overdue' : 'Account Scheduled for Deletion'}
            </h3>
            
            {isExpired ? (
              <div className="text-red-700 text-sm space-y-2">
                <p>
                  Your account was scheduled for deletion on <strong>{formattedDate}</strong>.
                  Please contact support immediately to recover your account.
                </p>
                <div className="bg-red-100 p-3 rounded border border-red-300">
                  <p className="font-medium">⚠️ Critical: Your account may be permanently deleted soon!</p>
                </div>
              </div>
            ) : (
              <div className="text-red-700 text-sm space-y-2">
                <p>
                  Your account is scheduled for permanent deletion on <strong>{formattedDate}</strong>.
                </p>
                <div className="flex items-center gap-2 text-lg font-semibold">
                  <FaCalendarAlt />
                  <span>{daysRemaining} day{daysRemaining !== 1 ? 's' : ''} remaining</span>
                </div>
                <p>
                  You can cancel this deletion request and reactivate your account at any time before the deletion date.
                </p>
              </div>
            )}

            <div className="mt-4 flex gap-3">
              {!isExpired && (
                <CustomButton
                  color="success"
                  label="Cancel Deletion & Reactivate"
                  onPress={onOpen}
                  startContent={<FaUndo size={14} />}
                />
              )}
              <CustomButton
                color="default"
                variant="flat"
                label="Contact Support"
                onPress={() => window.open('mailto:<EMAIL>', '_blank')}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Cancel Deletion Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        placement="center"
        backdrop="blur"
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">Cancel Account Deletion</ModalHeader>
          <ModalBody>
            {showAlert && (
              <Alert className="mb-4" color={alertType === 'success' ? 'success' : 'danger'}>
                {alertMessage}
              </Alert>
            )}

            <div className="bg-green-50 p-4 rounded border border-green-200 text-green-800 flex items-start gap-3 text-sm mb-4">
              <FaInfoCircle className="mt-0.5 text-green-600" />
              <div>
                <h4 className="font-semibold mb-2">Reactivate Your Account</h4>
                <ul className="list-disc pl-4 space-y-1">
                  <li>Your account deletion will be <strong>cancelled immediately</strong></li>
                  <li>All your data and settings will be <strong>restored</strong></li>
                  <li>You'll regain full access to your account</li>
                  <li>You can continue using all services normally</li>
                </ul>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              <p>
                <strong>Scheduled deletion date:</strong> {formattedDate}
              </p>
              <p>
                <strong>Days remaining:</strong> {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}
              </p>
              <p>
                <strong>Account email:</strong> {deletionInfo.email}
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <CustomButton
              color="default"
              variant="flat"
              label="Keep Deletion Scheduled"
              onPress={onClose}
            />
            <CustomButton
              color="success"
              label="Cancel Deletion & Reactivate"
              onPress={handleCancelDeletion}
              isLoading={isCancelling}
              startContent={<FaUndo size={14} />}
            />
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default AccountDeletionStatus;
