import * as filter from 'leo-profanity';

// Initialize the profanity filter
filter.loadDictionary('en'); // Load English dictionary
filter.loadDictionary(); // Load default dictionary

// Add custom words if needed (optional)
const customBadWords = [
  // Add any custom words you want to filter
  'spam',
  'scam',
  // Add more as needed
];

// Add custom words to the filter
customBadWords.forEach(word => {
  filter.add(word);
});

/**
 * Check if text contains profanity
 * @param text - The text to check
 * @returns boolean - true if profanity is found, false otherwise
 */
export const containsProfanity = (text: string): boolean => {
  if (!text || typeof text !== 'string') {
    return false;
  }
  
  return filter.check(text);
};

/**
 * Clean profanity from text by replacing with asterisks
 * @param text - The text to clean
 * @param replacement - Optional replacement character (default: '*')
 * @returns string - Cleaned text
 */
export const cleanProfanity = (text: string, replacement: string = '*'): string => {
  if (!text || typeof text !== 'string') {
    return text;
  }
  
  return filter.clean(text, replacement);
};

/**
 * Get list of profane words found in text
 * @param text - The text to check
 * @returns string[] - Array of profane words found
 */
export const getProfaneWords = (text: string): string[] => {
  if (!text || typeof text !== 'string') {
    return [];
  }
  
  return filter.list(text);
};

/**
 * Validate review content for profanity
 * @param reviewData - Object containing review fields to validate
 * @returns Object with validation results
 */
export interface ReviewValidationResult {
  isValid: boolean;
  errors: string[];
  cleanedData?: {
    title?: string;
    review?: string;
    comment?: string;
  };
}

export const validateReviewForProfanity = (reviewData: {
  title?: string;
  review?: string;
  comment?: string;
}): ReviewValidationResult => {
  const errors: string[] = [];
  const cleanedData: { title?: string; review?: string; comment?: string } = {};
  
  // Check title for profanity
  if (reviewData.title) {
    if (containsProfanity(reviewData.title)) {
      errors.push('Review title contains inappropriate language');
      cleanedData.title = cleanProfanity(reviewData.title);
    } else {
      cleanedData.title = reviewData.title;
    }
  }
  
  // Check review content for profanity
  if (reviewData.review) {
    if (containsProfanity(reviewData.review)) {
      errors.push('Review content contains inappropriate language');
      cleanedData.review = cleanProfanity(reviewData.review);
    } else {
      cleanedData.review = reviewData.review;
    }
  }
  
  // Check comment for profanity (if different from review)
  if (reviewData.comment && reviewData.comment !== reviewData.review) {
    if (containsProfanity(reviewData.comment)) {
      errors.push('Review comment contains inappropriate language');
      cleanedData.comment = cleanProfanity(reviewData.comment);
    } else {
      cleanedData.comment = reviewData.comment;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    cleanedData: errors.length > 0 ? cleanedData : undefined
  };
};

/**
 * Auto-clean profanity from review data
 * @param reviewData - Review data to clean
 * @returns Cleaned review data
 */
export const autoCleanReviewData = (reviewData: {
  title?: string;
  review?: string;
  comment?: string;
}): {
  title?: string;
  review?: string;
  comment?: string;
} => {
  return {
    title: reviewData.title ? cleanProfanity(reviewData.title) : reviewData.title,
    review: reviewData.review ? cleanProfanity(reviewData.review) : reviewData.review,
    comment: reviewData.comment ? cleanProfanity(reviewData.comment) : reviewData.comment,
  };
};

// Export the filter instance for advanced usage
export { filter };
