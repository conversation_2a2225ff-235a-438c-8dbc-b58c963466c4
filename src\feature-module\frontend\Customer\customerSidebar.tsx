import { useState, useEffect, useCallback } from 'react';
import { NavLink, Link, useNavigate } from 'react-router-dom';
import logo from '../../../assets/logo/1 (1).png';

import { useAuth } from 'react-oidc-context';
import {
  FaTh,
  FaHome,
  FaMobileAlt,
  FaHeart,
  FaWallet,
  FaStar,
  FaComments,
  FaCog,
  FaSignOutAlt,
  FaShieldAlt,
  FaBell,
  FaPlug,
  FaTrash,
  FaUserCircle,
} from 'react-icons/fa';

import { JSX } from 'react/jsx-runtime';
import CustomButton from '../../components/CustomButton';
import { apiClient } from '../../../api';
import { ProfilePictureDisplay } from '../../components/ProfilePicture';
import {
  getProfilePictureUrlFromName,
  getUserProfileWithPicture
} from '../../../service/profilePictureService';

const CustomerSidebar = () => {

  const [userName, setUserName] = useState('User');
  const [profileImage, setProfileImage] = useState('');
  const [profileImageUrl, setProfileImageUrl] = useState('');
  const [loading, setLoading] = useState(false);

  const auth = useAuth();
  const navigate = useNavigate();
  const handleGoHome = () => navigate('/');

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user) {
      return auth.user.profile.preferred_username ||
             auth.user.profile.sub ||
             auth.user.profile.email;
    }
    return null;
  }, [auth.user]);

  // Fetch user data from API using the profile service
  const fetchUserData = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) {
      console.log('Cannot fetch user data: No user ID or not authenticated');
      return;
    }

    try {
      setLoading(true);
      console.log(`Fetching user data for sidebar, ID: ${uid}`);

      // Use the profile service to get user data with proper profile picture handling
      const profileData = await getUserProfileWithPicture(uid);

      if (profileData) {
        console.log('User profile data fetched for sidebar:', profileData);


        // Set user name from the profile data
        setUserName(profileData.name || profileData.email?.split('@')[0] || 'User');

        // Handle profile picture - prioritize profilePicture field
        const profileImageName = profileData.profileImage || profileData.profilePicture || '';
        if (profileImageName) {
          console.log('Found profile image name:', profileImageName);

          // If it's just an image name (not a full URL), convert it to full URL
          if (profileImageName && !profileImageName.startsWith('http')) {
            const fullUrl = getProfilePictureUrlFromName(profileImageName);
            setProfileImage(profileImageName); // Store the image name
            setProfileImageUrl(fullUrl); // Store the full URL
            console.log('Profile image URL generated:', fullUrl);
          } else {
            // If it's already a full URL, use it directly
            setProfileImage(profileImageName);
            setProfileImageUrl(profileImageName);
          }
        } else {
          // Clear profile image if none found
          setProfileImage('');
          setProfileImageUrl('');
        }
      }
    } catch (error) {
      console.error('Error fetching user data for sidebar:', error);
      // Fallback to direct API call if profile service fails
      try {
        const response = await apiClient.get(`api/v1/user/${uid}`);
        if (response.status === 200 && response.data) {
          const userData = response.data.user || response.data;
          setUserName(userData.name || userData.email?.split('@')[0] || 'User');

          // Handle profile picture from direct API response
          const profileImageData = userData.profilePicture || userData.profileImage || '';
          if (profileImageData && !profileImageData.startsWith('http')) {
            const fullUrl = getProfilePictureUrlFromName(profileImageData);
            setProfileImage(profileImageData);
            setProfileImageUrl(fullUrl);
          } else if (profileImageData) {
            setProfileImage(profileImageData);
            setProfileImageUrl(profileImageData);
          }
        }
      } catch (fallbackError) {
        console.error('Fallback API call also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
    }
  }, [auth.isAuthenticated, getUserId]);

  // Get user name from auth context and fetch user data
  useEffect(() => {
    if (!auth.isLoading) {
      if (auth.isAuthenticated && auth.user) {
        console.log('User profile in sidebar:', auth.user.profile);

        // Try to get user's name from different possible profile fields
        const name = auth.user.profile.name ||
                    auth.user.profile.given_name ||
                    auth.user.profile.preferred_username ||
                    auth.user.profile.email?.split('@')[0] ||
                    'User';

        console.log('Using name for sidebar:', name);
        setUserName(name);

        // Fetch user data including profile image
        fetchUserData();
      } else {
        console.log('User not authenticated in sidebar');
        setUserName('Guest User');
        setProfileImage('');
        setProfileImageUrl('');

      }
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.user, fetchUserData]);

  // Listen for profile picture updates from localStorage or custom events
  useEffect(() => {
    const handleProfilePictureUpdate = () => {
      console.log('Profile picture update detected, refreshing sidebar data');
      fetchUserData();
    };

    // Listen for custom profile picture update events
    window.addEventListener('profilePictureUpdated', handleProfilePictureUpdate);

    // Also listen for storage events (if profile updates are stored in localStorage)
    window.addEventListener('storage', (e) => {
      if (e.key === 'profilePictureUpdated') {
        handleProfilePictureUpdate();
      }
    });

    return () => {
      window.removeEventListener('profilePictureUpdated', handleProfilePictureUpdate);
      window.removeEventListener('storage', handleProfilePictureUpdate);
    };
  }, [fetchUserData]);

  return (
    <nav className="bg-white border-r border-gray-200 h-screen fixed top-0 left-0 w-[260px] py-5 px-3 overflow-auto text-slate-700">
      <div className="flex items-center">
               <Link to="/">
                 <img
                   src={logo}
                   alt="Gig Mosaic Logo"
                   className="object-contain h-16 w-auto"
                 />
               </Link>
             </div>
             

        <hr className="my-6 border-gray-200" />

        <div>
          <h4 className="text-[11px] uppercase tracking-wide text-slate-500 mb-3 px-3">General</h4>
          <ul className="space-y-1 px-1 flex-1">
            <SidebarItem link="/customer/customer-dashboard" icon={<FaTh />} text="Dashboard" />
            <SidebarItem link="/customer/customer-booking" icon={<FaMobileAlt />} text="Bookings" />
            <SidebarItem link="/customer/customer-favourite" icon={<FaHeart />} text="Favorites" />
            <SidebarItem link="/customer/customer-reviews" icon={<FaStar />} text="Reviews" />
            <SidebarItem link="/customer/customer-chat" icon={<FaComments />} text="Chat" />
          </ul>
        </div>

        <hr className="my-6 border-gray-200" />

        <div>
          <h4 className="text-[11px] uppercase tracking-wide text-slate-500 mb-3 px-3">Settings</h4>
          <ul className="space-y-1 px-1 flex-1">
            <SidebarItem link="/customer/settings/customer-profile" icon={<FaCog />} text="Account Settings" />
            {/* <SidebarItem link="/customer/settings/customer-security" icon={<FaShieldAlt />} text="Security Settings" /> */}
            <SidebarItem link="/customer/settings/notification" icon={<FaBell />} text="Notifications" />
            <SidebarItem link="/customers/settings/connected-apps" icon={<FaPlug />} text="Connected Apps" />
          </ul>
        </div>

        <div className="mt-4">
          <ul className="space-y-1 px-1">
            <SidebarItem link="/authentication/login" icon={<FaSignOutAlt />} text="Home" />
          </ul>

          <div className="flex flex-wrap items-center cursor-pointer border-t border-gray-200 py-3 mt-6">
            <div className="w-10 h-10 rounded-md border-2 border-white overflow-hidden">
              {loading ? (
                <div className="w-full h-full flex items-center justify-center bg-gray-200 animate-pulse">
                  <FaUserCircle className="text-gray-400 text-2xl" />
                </div>
              ) : (
                <ProfilePictureDisplay
                  profileImage={profileImage}
                  profileImageUrl={profileImageUrl}
                  userName={userName}
                  size="sm"
                  className="w-full h-full"
                />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm text-slate-900 font-medium">{userName}</p>
              <p className="text-xs text-slate-500 mt-0.5">Active account</p>
            </div>
          </div>
        </div>

    </nav>
  );
};

// Reusable Sidebar Item Component
interface SidebarItemProps {
  link: string;
  icon?: JSX.Element;
  text: string;
  modalTarget?: string;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  link,
  icon,
  text,
  modalTarget,
}) => {
  return (
    <li>
      <NavLink
        to={link}
        className={({ isActive }) =>
          `flex items-center gap-3 text-sm px-3 py-2 rounded-md transition-colors ${
            isActive
              ? 'bg-gray-100 text-slate-900'
              : 'text-slate-700 hover:bg-gray-50 hover:text-slate-900'
          }`
        }
        {...(modalTarget
          ? { 'data-bs-toggle': 'modal', 'data-bs-target': modalTarget }
          : {})}
      >
        {icon && <span className="text-slate-500">{icon}</span>}
        <span>{text}</span>
      </NavLink>
    </li>
  );
};

export default CustomerSidebar;
