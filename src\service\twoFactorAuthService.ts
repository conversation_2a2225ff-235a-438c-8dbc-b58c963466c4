import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import { AxiosError } from 'axios';
import { getToken } from '../tokenprovider';

// TypeScript interfaces for 2FA operations
export interface SendEmail2FARequest {
  email: string;
}

export interface SendEmail2FAResponse {
  success: boolean;
  message: string;
  data?: {
    email: string;
    codeExpiry?: string;
    requestId?: string;
  };
}

export interface VerifyEmail2FARequest {
  email: string;
  code: string;
}

export interface VerifyEmail2FAResponse {
  success: boolean;
  message: string;
  data?: {
    email: string;
    twoFactorEnabled: boolean;
    backupCodes?: string[];
  };
}

export interface TwoFactorError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Send verification code to user's email for 2FA setup
 * @param email - User's email address
 * @returns Promise with API response
 */
export const sendEmail2FA = async (email: string): Promise<SendEmail2FAResponse> => {
  try {
    console.log(`Sending 2FA verification code to email: ${email}`);

    // Check authentication
    const token = getToken();
    console.log('Authentication token available:', !!token);
    if (!token) {
      console.warn('No authentication token found - user may not be logged in');
    }

    const requestData: SendEmail2FARequest = {
      email: email.trim().toLowerCase()
    };

    console.log('Request data:', requestData);
    console.log('API endpoint:', Path.sendEmail2FA);
    console.log('Full URL:', `${import.meta.env.VITE_APP_BACKEND_PORT}${Path.sendEmail2FA}`);

    // TEMPORARY: Uncomment the lines below to use mock response while debugging backend
    // console.log('🚧 Using mock response for testing');
    // return {
    //   success: true,
    //   message: 'Mock: Verification code sent to your email',
    //   data: { email }
    // };

    const response = await apiClient.post(Path.sendEmail2FA, requestData);

    if (response.status === 200 || response.status === 201) {
      console.log('2FA email sent successfully:', response.data);
      return {
        success: true,
        message: response.data.message || 'Verification code sent to your email',
        data: response.data.data || { email }
      };
    }

    // Handle unexpected success status codes
    console.warn('Unexpected response status:', response.status);
    return {
      success: false,
      message: 'Unexpected response from server'
    };

  } catch (error) {
    console.error('Error sending 2FA email:', error);

    if (error instanceof AxiosError) {
      // Log detailed error information for debugging
      console.error('Response status:', error.response?.status);
      console.error('Response data:', error.response?.data);
      console.error('Response headers:', error.response?.headers);
      console.error('Request config:', error.config);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.response?.data?.details ||
                          `Server error (${error.response?.status}): ${error.message}`;

      const errorCode = error.response?.data?.code || error.response?.status?.toString();

      // Handle specific status codes
      if (error.response?.status === 500) {
        console.error('Internal Server Error - Check backend logs');
        return {
          success: false,
          message: `Server error: ${errorMessage}. Please check backend logs for details.`,
          data: { email }
        };
      }

      return {
        success: false,
        message: errorMessage,
        data: { email }
      };
    }

    return {
      success: false,
      message: 'Network error occurred while sending verification code'
    };
  }
};

/**
 * Verify email 2FA code and enable two-factor authentication
 * @param email - User's email address
 * @param code - Verification code from email
 * @returns Promise with API response
 */
export const verifyEmail2FA = async (email: string, code: string): Promise<VerifyEmail2FAResponse> => {
  try {
    console.log(`Verifying 2FA code for email: ${email}`);

    const requestData: VerifyEmail2FARequest = {
      email: email.trim().toLowerCase(),
      code: code.trim()
    };

    const response = await apiClient.post(Path.verifyEmail2FA, requestData);

    if (response.status === 200 || response.status === 201) {
      console.log('2FA verification successful:', response.data);
      return {
        success: true,
        message: response.data.message || 'Two-factor authentication enabled successfully',
        data: response.data.data || { 
          email, 
          twoFactorEnabled: true 
        }
      };
    }

    // Handle unexpected success status codes
    console.warn('Unexpected response status:', response.status);
    return {
      success: false,
      message: 'Unexpected response from server'
    };

  } catch (error) {
    console.error('Error verifying 2FA code:', error);
    
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Failed to verify code';
      
      const errorCode = error.response?.data?.code || error.response?.status?.toString();
      
      // Handle specific error cases
      if (error.response?.status === 400) {
        return {
          success: false,
          message: errorMessage || 'Invalid verification code'
        };
      }
      
      if (error.response?.status === 404) {
        return {
          success: false,
          message: 'Verification code not found or expired'
        };
      }
      
      if (error.response?.status === 429) {
        return {
          success: false,
          message: 'Too many attempts. Please try again later'
        };
      }
      
      return {
        success: false,
        message: errorMessage
      };
    }

    return {
      success: false,
      message: 'Network error occurred while verifying code'
    };
  }
};

/**
 * Check if 2FA is enabled for the current user
 * @returns Promise with 2FA status
 */
export const check2FAStatus = async (): Promise<{ enabled: boolean; email?: string }> => {
  try {
    const response = await apiClient.get(Path.check2FAStatus);
    
    if (response.status === 200) {
      return {
        enabled: response.data.enabled || false,
        email: response.data.email
      };
    }
    
    return { enabled: false };
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    return { enabled: false };
  }
};

/**
 * Disable 2FA for the current user
 * @returns Promise with disable operation result
 */
export const disable2FA = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await apiClient.post(Path.disable2FA);
    
    if (response.status === 200) {
      return {
        success: true,
        message: response.data.message || 'Two-factor authentication disabled successfully'
      };
    }
    
    return {
      success: false,
      message: 'Failed to disable two-factor authentication'
    };
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    
    if (error instanceof AxiosError) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Failed to disable two-factor authentication';
      return {
        success: false,
        message: errorMessage
      };
    }
    
    return {
      success: false,
      message: 'Network error occurred while disabling 2FA'
    };
  }
};

/**
 * Validate email format
 * @param email - Email to validate
 * @returns boolean indicating if email is valid
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Validate verification code format
 * @param code - Code to validate
 * @returns boolean indicating if code format is valid
 */
export const validateVerificationCode = (code: string): boolean => {
  // Assuming 6-digit numeric code
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code.trim());
};

/**
 * Debug function to test backend connectivity and authentication
 * @returns Promise with debug information
 */
export const debug2FAEndpoint = async (): Promise<any> => {
  try {
    console.log('=== 2FA Debug Information ===');
    console.log('Backend URL:', import.meta.env.VITE_APP_BACKEND_PORT);
    console.log('Send Email 2FA Path:', Path.sendEmail2FA);
    console.log('Full Send Email URL:', `${import.meta.env.VITE_APP_BACKEND_PORT}${Path.sendEmail2FA}`);

    const token = getToken();
    console.log('Auth token available:', !!token);
    console.log('Auth token length:', token?.length || 0);

    // Try a simple test request to see if the endpoint exists
    try {
      const testResponse = await apiClient.get('/api/v1/auth/2fa/status');
      console.log('2FA Status endpoint test successful:', testResponse.status);
      return {
        success: true,
        message: 'Backend connectivity test passed',
        details: {
          backendUrl: import.meta.env.VITE_APP_BACKEND_PORT,
          hasToken: !!token,
          statusEndpointWorking: true
        }
      };
    } catch (error) {
      console.log('2FA Status endpoint test failed:', error);
      return {
        success: false,
        message: 'Backend connectivity test failed',
        error: error instanceof AxiosError ? error.response?.data : error,
        details: {
          backendUrl: import.meta.env.VITE_APP_BACKEND_PORT,
          hasToken: !!token,
          statusEndpointWorking: false
        }
      };
    }
  } catch (error) {
    console.error('Debug function error:', error);
    return {
      success: false,
      message: 'Debug function failed',
      error
    };
  }
};
