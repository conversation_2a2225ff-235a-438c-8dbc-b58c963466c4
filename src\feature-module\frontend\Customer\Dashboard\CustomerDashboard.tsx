import { FaShoppingCart, FaEye, FaUser, FaCheckCircle, FaTimesCircle, FaArrowRight, FaClock } from 'react-icons/fa';

import CustomButton from '../../../components/CustomButton';
import { Chip } from '@heroui/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuth } from 'react-oidc-context';

import { useNavigate } from 'react-router-dom';

import { toast } from 'react-toastify';
import { getUserBookings, cancelBooking, Booking as BookingType } from '../../../../service/bookingService';
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, PieChart, Pie, Cell, Legend } from 'recharts';
import { getImageUrlWithFallback } from '../aws/s3FileUpload';


export default function Dashboard() {
  const navigate = useNavigate();
  const [cancellingId, setCancellingId] = useState<string | number | null>(null);

  const handleViewAllBookings = useCallback(() => {
    navigate('/customer/customer-booking');
  }, [navigate]);

  // Open booking details in Booking List page
  const handleOpenBookingDetails = useCallback((booking: BookingType) => {
    const openBookingId = String(booking.bookingId || booking.id || '');
    if (!openBookingId) {
      toast.error('Unable to open booking: missing ID');
      return;
    }
    navigate('/customer/customer-booking', { state: { openBookingId } });
  }, [navigate]);


  // Booking data (from API)
  const auth = useAuth();
  const [bookingItems, setBookingItems] = useState<BookingType[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState(false);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user && auth.user.profile) {
      const profile = auth.user.profile as {
        preferred_username?: string;
        sub?: string;
        email?: string;
      };
      return profile.preferred_username || profile.sub || profile.email || null;
    }
    return null;
  }, [auth.user]);

  // Fetch recent bookings
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) return;

    try {
      setBookingsLoading(true);
      const response = await getUserBookings({ userId: uid, page: 1, limit: 6 });
      if (response && response.bookings) {
        setBookingItems(response.bookings);
      } else {
        setBookingItems([]);
      }
    } catch (error) {
      console.error('Failed to load bookings on dashboard', error);
      setBookingItems([]);
    } finally {
      setBookingsLoading(false);
    }
  }, [auth.isAuthenticated, getUserId]);

  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchBookings]);


  // Cancel a booking and refresh
  const handleCancelBooking = useCallback(async (id: string | number) => {
    try {
      setCancellingId(id);
      await cancelBooking(String(id));
      toast.success('Booking cancelled');
      await fetchBookings();
    } catch {
      toast.error('Failed to cancel booking');
    } finally {
      setCancellingId(null);
    }
  }, [fetchBookings]);

  // Get color classes for status
  type ChipColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  const getStatusColor = (status: string): ChipColor => {
    switch (status) {
      case 'Completed':
      case 'Finished':
        return 'success';
      case 'Pending':
      case 'Inprogress':
      case 'Confirmed':
        return 'primary';
      case 'Rescheduled':
        return 'warning';
      case 'Cancelled':
        return 'danger';
      default:
        return 'default';
    }
  };

  // StatusChip component for consistent status display
  const StatusChip = ({ status }: { status: string }) => (
    <Chip
      color={getStatusColor(status)}
      variant="flat"
      size="sm"
      radius="sm"
      className="font-medium"
    >
      {status}
    </Chip>
  );

  // Metrics derived from real bookings
  const metrics = useMemo(() => {
    const totalOrders = bookingItems.length;
    const totalCompleted = bookingItems.filter((b) => b.status === 'Completed' || b.status === 'Finished').length;
    const totalCancelled = bookingItems.filter((b) => b.status === 'Cancelled').length;
    const totalPending = bookingItems.filter((b) => !['Completed', 'Finished', 'Cancelled'].includes(String(b.status))).length;
    return { totalOrders, totalCompleted, totalCancelled, totalPending };
  }, [bookingItems]);

  // Chart data: status distribution
  const statusData = useMemo(() => {
    const buckets: Record<string, number> = {
      Completed: 0,
      Pending: 0,
      Rescheduled: 0,
      Cancelled: 0,
    };
    bookingItems.forEach((b) => {
      const raw = String(b.status || 'Pending');
      if (raw === 'Completed' || raw === 'Finished') buckets.Completed++;
      else if (raw === 'Cancelled') buckets.Cancelled++;
      else if (raw === 'Rescheduled') buckets.Rescheduled++;
      else buckets.Pending++;
    });
    return Object.entries(buckets)
      .filter(([, v]) => v > 0)
      .map(([name, value]) => ({ name, value }));
  }, [bookingItems]);

  const STATUS_COLORS: Record<string, string> = {
    Completed: '#22c55e',
    Pending: '#3b82f6',
    Rescheduled: '#f59e0b',
    Cancelled: '#ef4444',
  };

  // Chart data: bookings over time (recent items)
  const timeSeriesData = useMemo(() => {
    type Row = { label: string; ts: number; count: number };
    const map = new Map<string, Row>();

    bookingItems.forEach((b) => {
      const d = b.date || b.appointmentDate;
      if (!d) return;
      const dt = new Date(d);
      const ts = dt.getTime();
      if (isNaN(ts)) return;
      const label = dt.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
      const row = map.get(label) || { label, ts, count: 0 };
      row.count += 1;
      map.set(label, row);
    });

    return Array.from(map.values())
      .sort((a, b) => a.ts - b.ts)
      .map(({ label, count }) => ({ date: label, count }));
  }, [bookingItems]);

  // Derive a friendly display name for the header
  const displayName = useMemo(() => {
    const profile = auth?.user?.profile as { name?: string; email?: string; preferred_username?: string } | undefined;
    return profile?.name || profile?.preferred_username || profile?.email || 'There';
  }, [auth?.user?.profile]);

  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Header */}
      <div className="mb-6 md:mb-8">
       <div className="bg-gradient-to-r from-[#77fdf9] to-white border border-[#7ed6d3] rounded-2xl p-5 md:p-6 flex items-center justify-between">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-gray-800">Welcome back, {displayName} 👋</h2>
            <p className="text-gray-500 text-sm mt-1">Here’s a quick snapshot of your recent activity and bookings.</p>
          </div>
          <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
            <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
            <span>Synced</span>
          </div>
        </div>
      </div>

      {/* Key Stats */}
      <div className="grid grid-cols-12 gap-3 md:gap-6 mb-8">
        {[
          { title: 'Total Orders', value: metrics.totalOrders, icon: <FaShoppingCart />, iconBg: 'bg-blue-50', iconText: 'text-blue-600' },
          { title: 'Pending', value: metrics.totalPending, icon: <FaClock />, iconBg: 'bg-amber-50', iconText: 'text-amber-600' },
          { title: 'Completed', value: metrics.totalCompleted, icon: <FaCheckCircle />, iconBg: 'bg-green-50', iconText: 'text-green-600' },
          { title: 'Cancelled', value: metrics.totalCancelled, icon: <FaTimesCircle />, iconBg: 'bg-rose-50', iconText: 'text-rose-600' },
        ].map((stat, index) => (
          <div
            key={index}
            className="group col-span-12 sm:col-span-6 lg:col-span-3 bg-white p-4 sm:p-5 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 rounded-xl flex items-center justify-between hover:-translate-y-0.5"
          >
            <div className={`p-3 rounded-full text-lg flex items-center justify-center ${stat.iconBg} ${stat.iconText}`}>
              {stat.icon}
            </div>
            <div className="flex-1 ml-4">
              <p className="text-gray-500 text-sm font-medium">{stat.title}</p>
              <p className="text-xl font-semibold">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Insights */}
      <div className="grid grid-cols-12 gap-4 md:gap-6 mb-8">
        {/* Bookings Over Time */}
        <div className="col-span-12 lg:col-span-8 bg-white p-5 md:p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg text-gray-800">Bookings Over Time</h3>
          </div>
          <div className="h-64">
            {bookingsLoading ? (
              <div className="h-full animate-pulse">
                <div className="h-full w-full rounded-lg bg-gray-100" />
              </div>
            ) : timeSeriesData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={timeSeriesData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <defs>
                    <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.35} />
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.06} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                  <YAxis allowDecimals={false} tick={{ fontSize: 12 }} />
                  <Tooltip />
                  <Area type="monotone" dataKey="count" stroke="#3b82f6" strokeWidth={2} fillOpacity={1} fill="url(#colorBookings)" isAnimationActive={true} />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">No time series data yet</div>
            )}
          </div>
        </div>
        {/* Status Breakdown */}
        <div className="col-span-12 lg:col-span-4 bg-white p-5 md:p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg text-gray-800">Status Breakdown</h3>
          </div>
          <div className="h-64">
            {bookingsLoading ? (
              <div className="h-full animate-pulse">
                <div className="h-full w-full rounded-lg bg-gray-100" />
              </div>
            ) : statusData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie data={statusData} dataKey="value" nameKey="name" innerRadius={55} outerRadius={85} paddingAngle={4} isAnimationActive={true}>
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.name] || '#9ca3af'} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">No status data yet</div>
            )}
          </div>
        </div>
      </div>



        {/* Recent Bookings */}
        <div className="bg-white p-5 md:p-6 mt-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl">
          <div className="flex justify-between items-center mb-5">
            <h3 className="text-lg font-bold text-gray-800">Recent Bookings</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="flat"
              size="sm"
              radius="sm"
              endContent={<FaEye />}
              onPress={handleViewAllBookings}
              className="font-medium"
            />
          </div>
          <div className="space-y-3">
            {bookingItems.slice(0, 4).map((booking, index) => (
              <div
                key={index}
                className="group flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-white transition-colors duration-200 p-4 rounded-lg border border-gray-100 cursor-pointer"
                onClick={() => handleOpenBookingDetails(booking)}
                title="Click to view booking details"
              >
                <div className="flex items-center space-x-3 md:space-x-4 flex-1">
                  {/* <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center ring-1 ring-gray-200">
                    {Array.isArray(booking.serviceImages) && booking.serviceImages.length > 0 ? (
                      <img
                        src={booking.serviceImages[0]?.startsWith('http')
                          ? booking.serviceImages[0]
                          : getImageUrlWithFallback(booking.serviceImages[0] as string, 'service-images')}
                        alt={booking.serviceName || booking.service}
                        className="w-full h-full object-cover"
                        onError={(e) => { (e.target as HTMLImageElement).src = 'https://via.placeholder.com/64x64?text=Service'; }}
                      />
                    ) : (
                      <FaUser className="text-blue-600 text-xl" />
                    )}
                  </div> */}
                  <div className="flex-1">
                    <h4 className="text-base md:text-lg font-medium">{booking.serviceName || booking.service}</h4>
                    <p className="text-sm text-gray-500">
                      2025-08-21
                    </p>
                    <p className="text-sm text-gray-600 font-medium">{booking.provider}</p>
                    <p className="text-xs text-gray-500">{booking.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
                  <StatusChip status="Pending" />
                  <div className="flex gap-2 ml-2">
                    <CustomButton
                      label="View"
                      size="sm"
                      radius="sm"
                      color="primary"
                      variant="flat"
                      className="hidden xs:flex"
                      onPress={() => handleOpenBookingDetails(booking)}
                    />
                    {(booking.status === 'Pending' || booking.status === 'Confirmed' || booking.status === 'Inprogress') && (
                      <CustomButton
                        label={cancellingId === (booking.bookingId || booking.id) ? 'Cancelling...' : 'Cancel'}
                        size="sm"
                        radius="sm"
                        color="danger"
                        variant="flat"
                        isDisabled={!!cancellingId}
                        onPress={(e?: React.MouseEvent) => {
                          e?.stopPropagation?.();
                          handleCancelBooking(String(booking.bookingId || booking.id));
                        }}
                      />
                    )}
                  </div>
                  <FaArrowRight className="text-gray-400 group-hover:text-gray-600 transition-colors hidden sm:block" />
                </div>
              </div>
            ))}
          </div>
          {bookingsLoading && (
            <div className="text-center py-6 text-gray-500">Loading bookings...</div>
          )}
          {!bookingsLoading && bookingItems.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent bookings found</p>
            </div>
          )}
        </div>
      </div>
  );
}
